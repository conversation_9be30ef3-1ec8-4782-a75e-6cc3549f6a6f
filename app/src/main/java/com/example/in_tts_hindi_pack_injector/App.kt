package com.example.in_tts_hindi_pack_injector

import android.app.Application
import android.content.Context
import android.util.Log
import com.pax.dal.IDAL
import com.pax.neptunelite.api.NeptuneLiteUser

class App : Application() {
    var dal: IDAL? = null
        private set

    companion object {
        private lateinit var instance: App
        private const val TAG = "App"
        fun get(): App {
            return instance
        }
    }

    override fun onCreate() {
        super.onCreate()
        instance = this
        initNeptuneDal(applicationContext)
    }

    /**
     * 初始化 Neptune DAL。
     */
    private fun initNeptuneDal(context: Context) {
        dal = NeptuneLiteUser.getInstance().getDal(context);
        Log.d(TAG, "Neptune DAL initialized.")

    }
}