import com.google.type.Date
import com.google.type.TimeZone

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
}


fun releaseTime(): String {
    return Date().format("yyyyMMdd", TimeZone.getTimeZone("GMT+8"))
}

fun buildTime(): String {
    return Date().format("dd-MM-yyyy HH:mm:ss", TimeZone.getTimeZone("GMT+8"))
}

fun Date.format(pattern: String, timeZone: TimeZone): String {
    val formatter = SimpleDateFormat(pattern, Locale.getDefault())
    formatter.timeZone = timeZone
    return formatter.format(this)
}

// Load signing properties
val signingPropsFile = file("../signing.properties")
val signingProps = Properties()
if (signingPropsFile.exists()) {
    signingProps.load(FileInputStream(signingPropsFile))
}

android {
    namespace = "com.example.in_tts_hindi_pack_injector"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.example.in_tts_hindi_pack_injector"
        minSdk = 24
        targetSdk = 35
        versionCode = 1
        versionName = "1.0.0_${releaseTime()}"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        val currentDate = Date().format("ddMMYYYY", TimeZone.getTimeZone("GMT+8"))
        buildConfigField("String", "VERSION_DATE", "\"${currentDate}\"")

        ndk {
            abiFilters += listOf("armeabi")
        }
    }

    signingConfigs {
        create("debug") {
            // Debug signing config will use default debug keystore
        }
        create("release") {
            if (signingProps.containsKey("RELEASE_STORE_FILE") &&
                signingProps.containsKey("RELEASE_STORE_PASSWORD") &&
                signingProps.containsKey("RELEASE_KEY_ALIAS") &&
                signingProps.containsKey("RELEASE_KEY_PASSWORD")) {

                storeFile = file(signingProps["RELEASE_STORE_FILE"] as String)
                storePassword = signingProps["RELEASE_STORE_PASSWORD"] as String
                keyAlias = signingProps["RELEASE_KEY_ALIAS"] as String
                keyPassword = signingProps["RELEASE_KEY_PASSWORD"] as String
            }
        }
        create("autoTest") {
            // Will use debug signing for autoTest builds
        }
        create("releaseEmi") {
            // Will use release signing for releaseEmi builds
        }
    }

    buildTypes {
        debug {
            buildConfigField("Boolean", "RELEASE", "false")
            buildConfigField("Boolean", "NEED_REMOVE_CARD", "true")
            buildConfigField("Boolean", "USE_UAT_EMI_KEY", "true")
            buildConfigField("String", "BUILD_TIME", "\"${buildTime()}\"")
            isMinifyEnabled = false
            isShrinkResources = false
            signingConfig = signingConfigs.getByName("debug")
        }

        create("autoTest") {
            buildConfigField("Boolean", "RELEASE", "false")
            buildConfigField("Boolean", "NEED_REMOVE_CARD", "false")
            buildConfigField("Boolean", "USE_UAT_EMI_KEY", "true")
            buildConfigField("String", "BUILD_TIME", "\"${buildTime()}\"")
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("debug")
        }

        create("releaseEmi") {
            buildConfigField("Boolean", "RELEASE", "true")
            buildConfigField("Boolean", "NEED_REMOVE_CARD", "true")
            buildConfigField("Boolean", "USE_UAT_EMI_KEY", "true")
            buildConfigField("String", "BUILD_TIME", "\"${buildTime()}\"")
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }

        release {
            buildConfigField("Boolean", "RELEASE", "true")
            buildConfigField("Boolean", "NEED_REMOVE_CARD", "true")
            buildConfigField("Boolean", "USE_UAT_EMI_KEY", "false")
            buildConfigField("String", "BUILD_TIME", "\"${buildTime()}\"")
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
    }

    // Custom APK naming
    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            if (output is com.android.build.gradle.internal.api.BaseVariantOutputImpl) {
                val type = when (variant.buildType.name) {
                    "autoTest" -> "_autoTest"
                    "debug" -> "_debug"
                    "releaseEmi" -> "_uat_emi"
                    else -> ""
                }
                val fileName = "TTS_Hindi_Pack_Injector_V${variant.versionName}${type}.apk"
                output.outputFileName = fileName
            }
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        compose = true
        buildConfig = true
    }

    lint {
        abortOnError = false
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.androidx.work.runtime.ktx)
    implementation(files("libs/NeptuneLiteApi_V4.15.00_T_20250522.jar"))
    implementation(libs.protolite.well.known.types)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}